<?php if (isset($component)) { $__componentOriginalc65b15f8ec42fa9596a15595e2d5798f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc65b15f8ec42fa9596a15595e2d5798f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.multistep.form-wrapper','data' => ['steps' => $steps,'currentStep' => $step,'totalSteps' => $totalSteps,'editMode' => $editMode]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('multistep.form-wrapper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($step),'totalSteps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalSteps),'editMode' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($editMode)]); ?>
    <?php if($step === 1): ?>
                    <?php echo $__env->make('livewire.Medications.medications-form-steps.step1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 2): ?>
                    <?php echo $__env->make('livewire.Medications.medications-form-steps.step2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 3): ?>
                    <?php echo $__env->make('livewire.Medications.medications-form-steps.step3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc65b15f8ec42fa9596a15595e2d5798f)): ?>
<?php $attributes = $__attributesOriginalc65b15f8ec42fa9596a15595e2d5798f; ?>
<?php unset($__attributesOriginalc65b15f8ec42fa9596a15595e2d5798f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc65b15f8ec42fa9596a15595e2d5798f)): ?>
<?php $component = $__componentOriginalc65b15f8ec42fa9596a15595e2d5798f; ?>
<?php unset($__componentOriginalc65b15f8ec42fa9596a15595e2d5798f); ?>
<?php endif; ?>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/Medications/medications-form.blade.php ENDPATH**/ ?>