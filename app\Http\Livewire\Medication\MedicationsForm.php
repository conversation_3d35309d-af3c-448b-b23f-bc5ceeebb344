<?php

namespace App\Http\Livewire\Medication;

use App\Models\Medication;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;

class MedicationsForm extends Component
{
    use WithFileUploads;

    public $step = 1;
    public $editMode = false;
    public Medication $medication;

    // Dynamic steps configuration
    public $steps = [];
    public $totalSteps = 3;

    public function mount($medication = null, $editMode = false, $steps = null)
    {
        $this->editMode = $editMode;
        $this->initializeSteps($steps);

        if ($this->editMode && $medication) {
            $this->medication = $medication;
            $this->loadModelData();
        }
    }

    public function initializeSteps()
    {
        // Default steps
        $this->steps = [
            1 => 'Medication Details',
            2 => 'DispensePro Related',
            3 => 'Scripts Related'
        ];
        $this->totalSteps = 3;
    }

    public function loadModelData()
    {
        // Load model data into form fields
        // Example:
        // $this->field1 = $this->model->field1;
        // $this->field2 = $this->model->field2;
    }

    public function render()
    {
        return view('livewire.Medications.medications-form');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->step < $this->totalSteps) {
            $this->step++;
        }
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function goToStep($stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if ($this->editMode && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            $this->step = $stepNumber;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];

        switch ($this->step) {
            case 1:
                $rules = [
                    'medication.name' => 'required|string|max:255|unique:medications,name',
                ];
                break;

            case 2:
                $rules = [
                    'medication.ndc' => 'required|string|max:255|unique:medications,ndc',
                    'medication.dispensepro_medication_name' => 'required|string|max:255',
                ];
                break;

            case 3:
                $rules = [
                    'medication.refills' => 'required|numeric',
                    'medication.vial_quantity' => 'required|numeric',
                    'medication.days_supply' => 'required|numeric',
                    'medication.sig' => 'nullable|string|max:1024',
                    'medication.notes' => 'nullable|string|max:1024',
                ];
                break;
        }

        return $rules;
    }

    public function submit()
    {
        $this->validateCurrentStep();

        try {
            if ($this->editMode) {
                $this->updateMedication();
            } else {
                $this->createMedication();
            }

            session()->flash('success-message', $this->editMode ? 'Record updated successfully!' : 'Record created successfully!');
            return redirect()->route('medications.index');
        } catch (\Exception $e) {
            Log::error('Form save error: ' . $e->getMessage());
            session()->flash('error-message', 'An error occurred while saving. Please try again.');
        }
    }

    private function createMedication()
    {
        $medication = Medication::create([
            'name' => $this->name,
            'ndc'=> $this->ndc,
            'dispensepro_medication_name' => $this->dispensepro_medication_name,
            'vial_quantity' => $this->vial_quantity,
            'days_supply' => $this->days_supply,
            'sig' => $this->sig,
            'notes' => $this->notes,
            'refills' => $this->refills,
            'is_active' => 1,
        ]);
    }

    private function updateMedication()
    {
        $updateData = [
            'name' => $this->name,
            'ndc' => $this->ndc,
            'dispensepro_medication_name' => $this->dispensepro_medication_name,
            'vial_quantity' => $this->vial_quantity,
            'days_supply' => $this->days_supply,
            'sig' => $this->sig,
            'notes' => $this->notes,
            'refills' => $this->refills,
        ];

        $this->medication->update($updateData);
    }
}
